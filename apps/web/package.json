{"name": "@fitness-rewards/web", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "typecheck": "tsc --noEmit"}, "dependencies": {"@clerk/clerk-react": "^5.2.8", "@fitness-rewards/db": "workspace:*", "@fitness-rewards/shared": "workspace:*", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^3.9.0", "@hugeicons/core-free-icons": "^1.0.14", "@hugeicons/react": "^1.0.5", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@types/lodash-es": "^4.17.12", "boring-avatars": "^1.11.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.8", "dayjs": "^1.11.11", "lodash-es": "^4.17.21", "lucide-react": "^0.378.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.23.1", "tailwind-merge": "^3.3.0", "zod": "^3.23.8"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@types/react": "^18.3.2", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "tailwindcss-animate": "^1.0.7", "typescript": "^5.4.5", "vite": "^5.2.11", "vite-tsconfig-paths": "^4.3.2"}}