import React, { useState, useEffect } from 'react';
import { useMutation, useQuery } from 'convex/react';
import toast from 'react-hot-toast';
import { api } from '@db';
import { Settings, Palette } from 'lucide-react';
import { AnonymousNameThemeManager } from './AnonymousNameThemeManager';

export default function SettingsTab() {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get the current user to access their clientId
  const user = useQuery(api.functions.users.getCurrentUser);

  const clientConfig = useQuery(
    api.functions.clientConfigs.getClientConfig,
    user?.clientId ? { clientId: user.clientId } : 'skip'
  );

  const updateSettings = useMutation(api.functions.clientConfigs.updateConfig);

  const [formData, setFormData] = useState({
    notificationEmail: '',
    appUrl: '',
    fromEmail: '',
    avatarColors: '',
  });

  // Update form data when clientConfig loads
  useEffect(() => {
    if (clientConfig) {
      setFormData({
        notificationEmail: clientConfig.notificationEmail || '',
        appUrl: clientConfig.appUrl || '',
        fromEmail: clientConfig.fromEmail || '',
        avatarColors: clientConfig.branding?.avatarColors?.join(', ') || '',
      });
    }
  }, [clientConfig]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.clientId) {
      toast.error('Unable to save settings. Please try again.');
      return;
    }

    setIsSubmitting(true);

    const colorsArray = formData.avatarColors
      .split(',')
      .map((color) => color.trim())
      .filter((color) => /^#[0-9a-fA-F]{6}$/.test(color));

    if (
      formData.avatarColors.trim() !== '' &&
      colorsArray.length === 0 &&
      formData.avatarColors.split(',').filter((c) => c.trim() !== '').length > 0
    ) {
      toast.error('Please provide valid hex color codes (e.g., #D4A574).');
      setIsSubmitting(false);
      return;
    }

    try {
      await updateSettings({
        clientId: user.clientId,
        config: {
          notificationEmail: formData.notificationEmail,
          appUrl: formData.appUrl,
          fromEmail: formData.fromEmail,
          branding: {
            avatarColors: colorsArray,
          },
        },
      });
      toast.success('Settings saved successfully!');
    } catch (error) {
      toast.error('Failed to save settings. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange =
    (field: keyof typeof formData) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => ({ ...prev, [field]: e.target.value }));
    };

  // Show loading state while user is being fetched
  if (user === undefined) {
    return (
      <div>
        <div className="mb-6 flex items-center">
          <Settings className="mr-2 h-6 w-6 text-gray-600" />
          <h2 className="text-2xl font-bold">Notification Settings</h2>
        </div>
        <div className="rounded-lg border bg-white p-6 shadow-sm">
          <div className="animate-pulse">
            <div className="mb-4 h-4 w-3/4 rounded bg-gray-200"></div>
            <div className="h-4 w-1/2 rounded bg-gray-200"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6 flex items-center">
        <Settings className="mr-2 h-6 w-6 text-gray-600" />
        <h2 className="text-2xl font-bold">Notification Settings</h2>
      </div>

      <div className="rounded-lg border bg-white p-6 shadow-sm">
        <p className="mb-6 text-gray-600">
          Configure where staff notifications are sent for your organization.
        </p>

        <form onSubmit={handleSubmit} className="space-y-6">
          <h3 className="border-b border-gray-200 pb-2 text-lg font-medium text-gray-800">
            Notification Settings
          </h3>
          <div>
            <label
              htmlFor="notificationEmail"
              className="block text-sm font-medium text-gray-700"
            >
              Staff Notification Email
            </label>
            <input
              type="email"
              id="notificationEmail"
              value={formData.notificationEmail}
              onChange={handleInputChange('notificationEmail')}
              placeholder="<EMAIL>"
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
            />
            <p className="mt-1 text-sm text-gray-500">
              This email address will receive a notification for every new
              reward redemption.
            </p>
          </div>

          <div>
            <label
              htmlFor="appUrl"
              className="block text-sm font-medium text-gray-700"
            >
              Application URL
            </label>
            <input
              type="url"
              id="appUrl"
              value={formData.appUrl}
              onChange={handleInputChange('appUrl')}
              placeholder="https://yourapp.example.com"
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
            />
            <p className="mt-1 text-sm text-gray-500">
              The base URL of the application, used for generating links in
              emails.
            </p>
          </div>

          <div>
            <label
              htmlFor="fromEmail"
              className="block text-sm font-medium text-gray-700"
            >
              Sender Email Address
            </label>
            <input
              type="email"
              id="fromEmail"
              value={formData.fromEmail}
              onChange={handleInputChange('fromEmail')}
              placeholder="<EMAIL>"
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
            />
            <p className="mt-1 text-sm text-gray-500">
              The "From" address for emails sent to members. Must be a verified
              domain in Resend.
            </p>
          </div>

          <h3 className="border-b border-gray-200 pb-2 pt-4 text-lg font-medium text-gray-800">
            Branding Settings
          </h3>

          <div>
            <label
              htmlFor="avatarColors"
              className="block text-sm font-medium text-gray-700"
            >
              Anonymous Avatar Colors
            </label>
            <input
              type="text"
              id="avatarColors"
              value={formData.avatarColors}
              onChange={handleInputChange('avatarColors')}
              placeholder="#D4A574, #CD7F32, #F5F2E8"
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
            />
            <p className="mt-1 text-sm text-gray-500">
              Provide a comma-separated list of hex color codes for generated
              avatars.
            </p>
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {isSubmitting ? 'Saving...' : 'Save Settings'}
          </button>
        </form>
      </div>

      <div className="mt-12">
        <div className="rounded-lg border bg-white p-6 shadow-sm">
          <div className="mb-6 flex items-center">
            <Palette className="mr-3 h-6 w-6 text-gray-600" />
            <h2 className="text-xl font-bold">Anonymous Name Themes</h2>
          </div>
          <p className="mb-6 text-gray-600">
            Customize the words used to generate anonymous names for your
            members. If no themes are defined, system defaults will be used.
          </p>
          <AnonymousNameThemeManager />
        </div>
      </div>
    </div>
  );
}
