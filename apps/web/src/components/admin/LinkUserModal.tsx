import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@db';
import { Doc, Id } from '@db/types';
import toast from 'react-hot-toast';
import { debounce } from 'lodash-es';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Loader2 } from 'lucide-react';

interface LinkUserModalProps {
  event: Doc<'unmatchedEvents'>;
  isOpen: boolean;
  onClose: () => void;
}

export const LinkUserModal: React.FC<LinkUserModalProps> = ({
  event,
  isOpen,
  onClose,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUserId, setSelectedUserId] = useState<Id<'users'> | null>(
    null
  );
  const [activityTypeKey, setActivityTypeKey] = useState<string>('');
  const [notes, setNotes] = useState('');

  const searchResults = useQuery(
    api.functions.unmatchedEvents.searchUsersForMatching,
    searchTerm.length > 2 ? { searchTerm } : null
  );
  const activityTypes = useQuery(
    api.functions.unmatchedEvents.getActivityTypesForResolution
  );
  const resolveEvent = useMutation(
    api.functions.unmatchedEvents.resolveUnmatchedEvent
  );

  useEffect(() => {
    if (isOpen) {
      setSearchTerm(event.externalUserEmail || event.externalUserName || '');
      setSelectedUserId(null);
      setActivityTypeKey('');
      setNotes('');
    }
  }, [isOpen, event]);

  const debouncedSearch = debounce((value: string) => {
    setSearchTerm(value);
  }, 300);

  const handleResolve = async () => {
    if (!selectedUserId || !activityTypeKey) {
      toast.error('Please select a user and an activity type.');
      return;
    }

    await toast.promise(
      resolveEvent({
        eventId: event._id,
        userId: selectedUserId,
        activityTypeKey,
        notes,
      }),
      {
        loading: 'Linking event...',
        success: 'Event successfully linked!',
        error: 'Failed to link event.',
      }
    );
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Link Unmatched Event</DialogTitle>
          <DialogDescription>
            Match this event to a user in your system to award them points.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div>
            <h4 className="font-semibold">Event Details</h4>
            <p>
              <strong>External User:</strong> {event.externalUserName} (
              {event.externalUserEmail})
            </p>
            <p>
              <strong>Provider:</strong> {event.provider}
            </p>
            <p>
              <strong>Received:</strong>{' '}
              {new Date(event.receivedAt).toLocaleString()}
            </p>
          </div>
          <div className="space-y-2">
            <label>Find and Select User</label>
            <Input
              placeholder="Search by name or email..."
              defaultValue={
                event.externalUserEmail || event.externalUserName || ''
              }
              onChange={(e) => debouncedSearch(e.target.value)}
            />
            {searchResults && (
              <div className="max-h-32 overflow-y-auto rounded-md border">
                {searchResults.map((user) => (
                  <div
                    key={user._id}
                    className={`hover:bg-muted cursor-pointer p-2 ${
                      selectedUserId === user._id ? 'bg-muted' : ''
                    }`}
                    onClick={() => setSelectedUserId(user._id)}
                  >
                    {user.displayName} ({user.email})
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="space-y-2">
            <label>Activity Type to Award</label>
            <Select onValueChange={setActivityTypeKey} value={activityTypeKey}>
              <SelectTrigger>
                <SelectValue placeholder="Select an activity type" />
              </SelectTrigger>
              <SelectContent>
                {activityTypes?.map((at) => (
                  <SelectItem key={at.key} value={at.key}>
                    {at.name} ({at.points} points)
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <label>Notes (Optional)</label>
            <Textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="e.g., Manually linked due to email mismatch."
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleResolve}
            disabled={
              !selectedUserId || !activityTypeKey || resolveEvent.isPending
            }
          >
            {resolveEvent.isPending && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Link Event
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
