import React, { useState, useMemo } from 'react';
import { useQuery, useMutation, InferQueryOutput } from 'convex/react';
import toast from 'react-hot-toast';
import { api } from '@db';
import { CheckCircle, Search } from 'lucide-react';
import { Id } from '@db/_generated/dataModel';
import { WithLoading } from '../utils/WithLoading';
import { ConfirmationDialog } from '../ui/ConfirmationDialog';

type Redemption = InferQueryOutput<
  typeof api.functions.userRedemptions.listPending
>[number];

type FulfillmentCandidate = {
  id: Id<'userRedemptions'>;
  rewardName: string;
  userName: string;
};

/**
 * Renders the tab for fulfilling reward redemptions.
 * Allows staff to search for members, view their pending/fulfilled redemptions,
 * and mark them as fulfilled.
 */
export default function RedemptionFulfillmentTab() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUserId, setSelectedUserId] = useState<Id<'users'> | null>(
    null
  );
  const [fulfillmentCandidate, setFulfillmentCandidate] =
    useState<FulfillmentCandidate | null>(null);

  // Use the new search query
  const searchResults = useQuery(
    api.functions.userRedemptions.searchUsers,
    searchQuery ? { search: searchQuery } : 'skip'
  );

  // Fetch pending redemptions if no user is selected
  const pendingRedemptions = useQuery(
    api.functions.userRedemptions.listPending,
    selectedUserId ? 'skip' : undefined
  );

  // Fetch all redemptions for a specific user if one is selected
  const userRedemptions = useQuery(
    api.functions.userRedemptions.listForUser,
    selectedUserId ? { userId: selectedUserId } : 'skip'
  );

  // Use the new fulfill mutation
  const fulfillRedemption = useMutation(api.functions.userRedemptions.fulfill);

  const handleFulfillClick = (redemption: Redemption) => {
    setFulfillmentCandidate({
      id: redemption._id,
      rewardName: redemption.rewardName,
      userName: redemption.userName,
    });
  };

  const executeFulfillment = () => {
    if (!fulfillmentCandidate) return;

    toast.promise(
      fulfillRedemption({ redemptionId: fulfillmentCandidate.id }),
      {
        loading: 'Fulfilling...',
        success: `Redemption fulfilled for ${fulfillmentCandidate.userName}!`,
        error: 'Failed to fulfill redemption.',
      }
    );
  };

  const selectedUser = useMemo(() => {
    if (!selectedUserId) return null;
    // Find user from any of the lists to display their name
    const allUsers = [
      ...(searchResults || []),
      ...(pendingRedemptions?.map((r) => ({
        _id: r.userId,
        firstName: r.userName.split(' ')[0],
        lastName: r.userName.split(' ')[1],
      })) || []),
      ...(userRedemptions?.map((r) => ({
        _id: r.userId,
        firstName: r.userName.split(' ')[0],
        lastName: r.userName.split(' ')[1],
      })) || []),
    ];
    const user = searchResults?.find((u) => u._id === selectedUserId);
    if (user) return user;

    // If not in search results, maybe they were in the initial list
    const redemptionForUser =
      pendingRedemptions?.find((r) => r.userId === selectedUserId) ??
      userRedemptions?.find((r) => r.userId === selectedUserId);
    if (redemptionForUser)
      return {
        _id: redemptionForUser.userId,
        firstName: redemptionForUser.userName.split(' ')[0],
        lastName: redemptionForUser.userName.split(' ')[1] ?? '',
      };

    return null;
  }, [selectedUserId, searchResults, pendingRedemptions, userRedemptions]);

  const redemptionsToShow = selectedUserId
    ? userRedemptions
    : pendingRedemptions;
  const isLoadingRedemptions = selectedUserId
    ? userRedemptions === undefined
    : pendingRedemptions === undefined;

  return (
    <div>
      <h2 className="mb-4 text-2xl font-bold">Reward Fulfillment</h2>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        {/* User Search and List */}
        <div className="rounded-lg border bg-white p-4 md:col-span-1">
          <div className="flex items-center justify-between">
            <h3 className="mb-2 text-lg font-semibold">Find Member</h3>
            {selectedUserId && (
              <button
                onClick={() => {
                  setSelectedUserId(null);
                  setSearchQuery('');
                }}
                className="text-sm text-indigo-600 hover:underline"
              >
                Clear
              </button>
            )}
          </div>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search by name or email..."
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value);
                setSelectedUserId(null); // Clear selection when search changes
              }}
              className="w-full rounded-md border-gray-300 pl-10"
            />
          </div>
          <div className="mt-4 max-h-96 space-y-2 overflow-y-auto">
            <WithLoading
              isPending={searchResults === undefined && !!searchQuery}
            >
              {searchQuery && searchResults?.length === 0 && (
                <p className="text-center text-sm text-gray-500">
                  No members found.
                </p>
              )}
              {searchResults?.map((user) => (
                <div
                  key={user._id}
                  onClick={() => {
                    setSelectedUserId(user._id);
                    setSearchQuery('');
                  }}
                  className={`cursor-pointer rounded-md p-2 ${
                    selectedUserId === user._id
                      ? 'bg-indigo-100 ring-2 ring-indigo-500'
                      : 'hover:bg-gray-100'
                  }`}
                >
                  <p className="font-semibold">
                    {user.firstName} {user.lastName}
                  </p>
                  <p className="text-sm text-gray-500">{user.email}</p>
                </div>
              ))}
            </WithLoading>
          </div>
        </div>

        {/* Redemptions List */}
        <div className="rounded-lg border bg-white p-4 md:col-span-2">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-lg font-semibold">
              {selectedUser
                ? `Redemptions for ${selectedUser.firstName} ${selectedUser.lastName}`
                : 'All Pending Redemptions'}
            </h3>
          </div>

          <div className="space-y-3">
            <WithLoading isPending={isLoadingRedemptions}>
              {redemptionsToShow && redemptionsToShow.length === 0 ? (
                <p className="py-8 text-center text-gray-500">
                  No redemptions found.
                </p>
              ) : (
                redemptionsToShow?.map((r: Redemption) => (
                  <div
                    key={r._id}
                    className="flex items-start justify-between rounded-lg bg-gray-50 p-3"
                  >
                    <div className="flex-1">
                      <p className="font-bold text-gray-800">{r.rewardName}</p>
                      {!selectedUserId && (
                        <p className="text-sm text-gray-600">
                          Member: {r.userName}
                        </p>
                      )}
                      <p className="text-xs text-gray-500">
                        Redeemed on:{' '}
                        {new Date(r.redemptionTimestamp).toLocaleDateString()}
                      </p>
                      {r.status === 'Fulfilled' &&
                        r.fulfilledAt &&
                        r.fulfilledByName && (
                          <p className="mt-1 text-xs text-gray-500">
                            Fulfilled by {r.fulfilledByName} on{' '}
                            {new Date(r.fulfilledAt).toLocaleDateString()}
                          </p>
                        )}
                    </div>
                    {r.status === 'Pending' && (
                      <button
                        onClick={() => handleFulfillClick(r)}
                        className="ml-4 inline-flex items-center justify-center rounded-md border border-transparent bg-green-600 px-3 py-1 text-sm font-medium text-white shadow-sm hover:bg-green-700"
                      >
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Fulfill
                      </button>
                    )}
                  </div>
                ))
              )}
            </WithLoading>
          </div>
        </div>
      </div>

      {fulfillmentCandidate && (
        <ConfirmationDialog
          isOpen={!!fulfillmentCandidate}
          onClose={() => setFulfillmentCandidate(null)}
          onConfirm={executeFulfillment}
          title="Confirm Reward Fulfillment"
          description={`Are you sure you want to fulfill the "${fulfillmentCandidate.rewardName}" for ${fulfillmentCandidate.userName}? This action cannot be undone.`}
          confirmButtonText="Yes, Fulfill"
        />
      )}
    </div>
  );
}
