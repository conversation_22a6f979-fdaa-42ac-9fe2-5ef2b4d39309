'use client';

import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@fitness-rewards/db';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import toast from 'react-hot-toast';
import {
  Loader2,
  CheckCircle,
  XCircle,
  Settings,
  ExternalLink,
  AlertTriangle,
  Users,
  Wrench,
} from 'lucide-react';

interface IntegrationsTabProps {
  onReviewEvents: () => void;
}

interface IntegrationCardProps {
  provider: string;
  displayName: string;
  description: string;
  isConfigured: boolean;
  isActive: boolean;
  status: 'Ready' | 'Pending';
  lastTestStatus?: 'success' | 'failed';
  lastTestAt?: number;
  onConfigure: () => void;
}

function IntegrationCard({
  provider,
  displayName,
  description,
  isConfigured,
  isActive,
  status,
  lastTestStatus,
  lastTestAt,
  onConfigure,
}: IntegrationCardProps) {
  const getStatusBadge = () => {
    if (status === 'Pending') {
      return (
        <Badge variant="outline">
          <Wrench className="mr-1 h-3 w-3" />
          Pending
        </Badge>
      );
    }
    if (!isConfigured) {
      return <Badge variant="secondary">Not Connected</Badge>;
    }
    if (!isActive) {
      return <Badge variant="destructive">Inactive</Badge>;
    }
    if (lastTestStatus === 'success') {
      return (
        <Badge variant="default" className="bg-green-500">
          <CheckCircle className="mr-1 h-3 w-3" />
          Connected
        </Badge>
      );
    }
    if (lastTestStatus === 'failed') {
      return (
        <Badge variant="destructive">
          <XCircle className="mr-1 h-3 w-3" />
          Error
        </Badge>
      );
    }
    return <Badge variant="outline">Unknown</Badge>;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {displayName}
              {getStatusBadge()}
            </CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <Button
            onClick={onConfigure}
            variant="outline"
            size="sm"
            disabled={status === 'Pending'}
          >
            <Settings className="mr-2 h-4 w-4" />
            Configure
          </Button>
        </div>
      </CardHeader>
      {isConfigured && (
        <CardContent>
          <div className="text-muted-foreground text-sm">
            {lastTestAt && (
              <p>Last tested: {new Date(lastTestAt).toLocaleString()}</p>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  );
}

interface ConfigurationModalProps {
  provider: string;
  displayName: string;
  isOpen: boolean;
  onClose: () => void;
}

function ConfigurationModal({
  provider,
  displayName,
  isOpen,
  onClose,
}: ConfigurationModalProps) {
  const [apiKey, setApiKey] = useState('');
  const [webhookSecret, setWebhookSecret] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const configureIntegration = useMutation(
    api.functions.integrations.configureIntegration
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!apiKey.trim()) {
      toast.error('API Key is required');
      return;
    }

    setIsLoading(true);
    try {
      const result = await configureIntegration({
        provider,
        apiKey: apiKey.trim(),
        webhookSecret: webhookSecret.trim() || undefined,
      });

      if (result.testResult.success) {
        toast.success(`${displayName} integration configured successfully!`);
        onClose();
        setApiKey('');
        setWebhookSecret('');
      } else {
        toast.error(`Configuration failed: ${result.testResult.error}`);
      }
    } catch (error) {
      toast.error('Failed to configure integration');
      console.error('Configuration error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getInstructions = () => {
    switch (provider) {
      case 'marianaTek':
        return (
          <div className="text-muted-foreground space-y-2 text-sm">
            <p>To configure Mariana-Tek integration:</p>
            <ol className="ml-4 list-inside list-decimal space-y-1">
              <li>Contact <EMAIL> to request API access</li>
              <li>Agree to the API Terms of Service</li>
              <li>Obtain your API key from the Mariana-Tek admin panel</li>
              <li>Enter the API key below and test the connection</li>
            </ol>
            <p className="mt-2">
              <ExternalLink className="mr-1 inline h-3 w-3" />
              <a
                href="https://docs.marianatek.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                View Mariana-Tek API Documentation
              </a>
            </p>
          </div>
        );
      case 'classpass':
        return (
          <div className="text-muted-foreground space-y-2 text-sm">
            <p>
              Classpass integration is currently in development and will be
              available soon.
            </p>
            <p className="mt-2">
              It will likely support real-time webhook-based data sync for
              seamless activity logging.
            </p>
          </div>
        );
      case 'mindBody':
        return (
          <div className="text-muted-foreground space-y-2 text-sm">
            <p>
              MindBody integration is planned for a future release. Please check
              back for updates.
            </p>
          </div>
        );
      default:
        return (
          <p className="text-muted-foreground text-sm">
            Enter your API credentials below.
          </p>
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Configure {displayName} Integration</DialogTitle>
          <DialogDescription>
            Set up automated activity logging from {displayName}.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {getInstructions()}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="apiKey">API Key *</Label>
              <Input
                id="apiKey"
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="Enter your API key"
                required
              />
            </div>

            {provider === 'marianaTek' && (
              <div className="space-y-2">
                <Label htmlFor="webhookSecret">Webhook Secret (Optional)</Label>
                <Input
                  id="webhookSecret"
                  type="password"
                  value={webhookSecret}
                  onChange={(e) => setWebhookSecret(e.target.value)}
                  placeholder="Enter webhook secret if available"
                />
                <p className="text-muted-foreground text-xs">
                  Note: Mariana-Tek currently uses polling instead of webhooks
                  for check-in events.
                </p>
              </div>
            )}

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Test & Save
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default function IntegrationsTab({
  onReviewEvents,
}: IntegrationsTabProps) {
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    provider: string;
    displayName: string;
  }>({ isOpen: false, provider: '', displayName: '' });

  const clientConfig = useQuery(
    api.functions.clientConfigs.getClientConfiguration
  );
  const integrationStatus = useQuery(
    api.functions.integrations.getIntegrationSyncStatus
  );
  const unmatchedEventsCount = useQuery(
    api.functions.unmatchedEvents.getUnmatchedEventsCount
  );

  const availableIntegrations = [
    {
      provider: 'marianaTek',
      displayName: 'Mariana-Tek',
      description: 'Automated polling for class check-in data.',
      status: 'Ready' as const,
    },
    {
      provider: 'classpass',
      displayName: 'Classpass',
      description: 'Real-time activity sync via webhooks.',
      status: 'Pending' as const,
    },
    {
      provider: 'mindBody',
      displayName: 'MindBody',
      description: 'Activity sync for studios using MindBody.',
      status: 'Pending' as const,
    },
  ];

  const handleConfigure = (provider: string, displayName: string) => {
    setModalState({ isOpen: true, provider, displayName });
  };

  const handleCloseModal = () => {
    setModalState({ isOpen: false, provider: '', displayName: '' });
  };

  const getIntegrationData = (provider: string) => {
    const status = integrationStatus?.find((s) => s.provider === provider);
    const config = clientConfig?.integrations?.find(
      (i) => i.provider === provider
    );
    return {
      isConfigured: !!config?.apiKey,
      isActive: config?.isActive ?? false,
      lastTestStatus: status?.lastTestStatus,
      lastTestAt: status?.lastTestAt,
    };
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Available Integrations</CardTitle>
          <CardDescription>
            Connect your studio's booking software to automate activity logging
            for your members.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {availableIntegrations.map((integration) => {
            const data = getIntegrationData(integration.provider);
            return (
              <IntegrationCard
                key={integration.provider}
                provider={integration.provider}
                displayName={integration.displayName}
                description={integration.description}
                status={integration.status}
                isConfigured={data.isConfigured}
                isActive={data.isActive}
                lastTestStatus={data.lastTestStatus}
                lastTestAt={data.lastTestAt}
                onConfigure={() =>
                  handleConfigure(integration.provider, integration.displayName)
                }
              />
            );
          })}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Unmatched Events</CardTitle>
              <CardDescription>
                Review check-in events that could not be automatically matched
                to a user.
              </CardDescription>
            </div>
            {unmatchedEventsCount !== undefined && unmatchedEventsCount > 0 && (
              <Button onClick={onReviewEvents}>
                <Users className="mr-2 h-4 w-4" />
                Review ({unmatchedEventsCount})
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground flex items-center text-sm">
            <AlertTriangle className="mr-2 h-4 w-4 text-yellow-500" />
            <p>
              Regularly reviewing and linking unmatched events ensures all
              members receive points and improves future automatic matching.
            </p>
          </div>
          {unmatchedEventsCount === 0 && (
            <p className="text-muted-foreground mt-4 text-center text-sm">
              No unmatched events to review. Great job!
            </p>
          )}
        </CardContent>
      </Card>

      <ConfigurationModal
        isOpen={modalState.isOpen}
        onClose={handleCloseModal}
        provider={modalState.provider}
        displayName={modalState.displayName}
      />
    </div>
  );
}
