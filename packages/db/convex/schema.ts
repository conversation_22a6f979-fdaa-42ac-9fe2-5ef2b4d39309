import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

export default defineSchema({
  users: defineTable({
    clerkUserId: v.string(),
    email: v.string(),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    clientId: v.optional(v.id('clients')),
    points: v.number(),
    tier: v.string(),
    role: v.optional(v.string()),
    searchText: v.optional(v.string()),
    // External system integration IDs for automated activity logging
    externalIds: v.optional(
      v.array(
        v.object({
          provider: v.string(), // e.g., "marianaTek", "mindBody"
          userId: v.string(), // The user's ID in the external system
        })
      )
    ),
    leaderboardSettings: v.optional(
      v.object({
        showRealName: v.boolean(),
        anonymousName: v.string(),
        avatarSeed: v.string(),
      })
    ),
  })
    .index('by_clerk_user_id', ['clerkUserId'])
    .index('by_email', ['email'])
    .searchIndex('by_searchText', {
      searchField: 'searchText',
      filterFields: ['clientId'],
    }),

  clients: defineTable({
    name: v.string(),
    slug: v.string(),
    createdAt: v.number(),
  }).index('by_slug', ['slug']),

  activityTypes: defineTable({
    clientId: v.id('clients'),
    name: v.string(), // e.g., "Class Attendance"
    key: v.string(), // e.g., "class_attendance"
    iconUrl: v.optional(v.string()),
    points: v.number(),
  }).index('by_client_id', ['clientId']),

  milestones: defineTable({
    clientId: v.id('clients'),
    name: v.string(),
    description: v.optional(v.string()),
    iconUrl: v.string(),
    triggerType: v.string(), // e.g., "activity_count"
    conditions: v.object({
      activityTypeMatcher: v.string(),
      countThreshold: v.number(),
    }),
    rewards: v.array(
      v.object({
        type: v.string(), // e.g., "points"
        value: v.union(v.string(), v.number()),
      })
    ),
    isEnabled: v.boolean(),
    isRepeatable: v.boolean(),
    createdBy: v.id('users'),
    lastModifiedBy: v.id('users'),
  })
    .index('by_client_id', ['clientId'])
    .index('by_client_id_and_name', ['clientId', 'name']),

  rewards: defineTable({
    clientId: v.id('clients'),
    name: v.string(),
    description: v.optional(v.string()),
    cost: v.number(),
    imageUrl: v.optional(v.string()),
    isActive: v.boolean(),
  }).index('by_client_id', ['clientId']),

  clientConfiguration: defineTable({
    clientId: v.id('clients'),
    branding: v.object({
      primaryColor: v.optional(v.string()),
      logoUrl: v.optional(v.string()),
      avatarColors: v.optional(v.array(v.string())),
    }),
    features: v.object({
      tiersEnabled: v.optional(v.boolean()),
      leaderboardsEnabled: v.optional(v.boolean()),
      socialSharingActive: v.optional(v.boolean()),
    }),
    // DEPRECATED: Milestone definitions are now stored in the `milestones` table.
    // This will be removed in a future migration.
    milestones: v.array(v.any()),
    dashboardLayout: v.object({
      layout: v.string(),
      widgets: v.array(v.any()),
    }),
    notificationEmail: v.optional(v.string()),
    appUrl: v.optional(v.string()),
    fromEmail: v.optional(v.string()),
  }).index('by_client_id', ['clientId']),

  anonymousNameTemplates: defineTable({
    clientId: v.id('clients'),
    themeName: v.string(),
    adjectives: v.array(v.string()),
    nouns: v.array(v.string()),
  }).index('by_client_id', ['clientId']),

  userRedemptions: defineTable({
    userId: v.id('users'),
    clientId: v.id('clients'),
    rewardId: v.id('rewards'),
    rewardName: v.optional(v.string()),
    pointsSpent: v.optional(v.number()),
    redemptionTimestamp: v.number(),
    status: v.union(v.literal('Pending'), v.literal('Fulfilled')),
    fulfilledAt: v.optional(v.number()),
    fulfilledBy: v.optional(v.id('users')),
  })
    .index('by_user_id', ['userId'])
    .index('by_reward_id', ['rewardId'])
    .index('by_status', ['status']),

  // New tables for activity tracking and milestone progress
  activities: defineTable({
    userId: v.id('users'),
    clientId: v.id('clients'),
    activityType: v.string(),
    timestamp: v.number(),
    pointsAwarded: v.optional(v.number()),
    metadata: v.optional(v.object({})),
    // Source tracking for automated vs manual logging
    source: v.optional(
      v.union(v.literal('manual'), v.literal('webhook'), v.literal('api_poll'))
    ),
    // External system reference for audit trail
    externalReference: v.optional(
      v.object({
        provider: v.string(),
        externalId: v.string(),
        externalTimestamp: v.optional(v.number()),
      })
    ),
  })
    .index('by_user_id', ['userId'])
    .index('by_user_activity_type', ['userId', 'activityType'])
    .index('by_userId_timestamp', ['userId', 'timestamp'])
    .index('by_source', ['source']),

  userMilestoneProgress: defineTable({
    userId: v.id('users'),
    milestoneId: v.string(),
    achievedAt: v.number(),
    pointsEarned: v.number(),
    badgesEarned: v.array(v.string()),
    // Denormalized fields for display purposes
    milestoneName: v.optional(v.string()),
    milestoneDescription: v.optional(v.string()),
    milestoneConditions: v.optional(
      v.object({
        activityTypeMatcher: v.string(),
        countThreshold: v.number(),
      })
    ),
    isRepeatable: v.optional(v.boolean()),
    milestoneIconUrl: v.string(),
  })
    .index('by_user_id', ['userId'])
    .index('by_milestone_id', ['milestoneId'])
    .index('by_achieved_at', ['achievedAt'])
    .index('by_user_id_achieved_at', ['userId', 'achievedAt']),

  userActivityCounts: defineTable({
    userId: v.id('users'),
    activityTypeKey: v.string(),
    count: v.number(),
  })
    .index('by_user_and_activity', ['userId', 'activityTypeKey'])
    .index('by_activity_type', ['activityTypeKey']),

  // Tier progression tracking table as per TIER_SYSTEM_PRD
  tierProgressions: defineTable({
    userId: v.id('users'),
    previousTier: v.string(),
    newTier: v.string(),
    pointsAtAdvancement: v.number(),
    advancedAt: v.number(),
    clientId: v.id('clients'),
  })
    .index('by_user_id', ['userId'])
    .index('by_advancement_date', ['advancedAt']),

  cheers: defineTable({
    cheererId: v.id('users'),
    cheeredId: v.id('users'),
    clientId: v.id('clients'),
    timestamp: v.number(),
  })
    .index('by_cheered_user', ['cheeredId'])
    .index('by_cheerer_and_cheered', ['cheererId', 'cheeredId']),

  // API Integration Tables for Automated Activity Integration
  clientApiCredentials: defineTable({
    clientId: v.id('clients'),
    provider: v.string(), // "marianaTek", "mindBody"
    encryptedApiKey: v.string(), // API key encrypted at rest
    encryptedWebhookSecret: v.optional(v.string()), // Webhook signing secret encrypted at rest
    isActive: v.boolean(),
    lastTestAt: v.optional(v.number()),
    lastTestStatus: v.optional(
      v.union(v.literal('success'), v.literal('failed'))
    ),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index('by_client_and_provider', ['clientId', 'provider']),

  // Store unmatched check-in events for admin review
  unmatchedEvents: defineTable({
    clientId: v.id('clients'),
    provider: v.string(),
    externalUserId: v.string(),
    externalUserEmail: v.optional(v.string()),
    externalUserName: v.optional(v.string()),
    eventType: v.string(), // e.g., "check_in"
    eventData: v.object({}), // Raw event payload
    receivedAt: v.number(),
    status: v.union(
      v.literal('pending'),
      v.literal('resolved'),
      v.literal('ignored')
    ),
    createdAt: v.number(),
    updatedAt: v.number(),
    resolvedAt: v.optional(v.number()),
    resolvedBy: v.optional(v.id('users')),
    notes: v.optional(v.string()),
  })
    .index('by_client_and_status', ['clientId', 'status'])
    .index('by_provider_and_external_user', ['provider', 'externalUserId']),

  // Track integration sync status and last poll times
  integrationSyncStatus: defineTable({
    clientId: v.id('clients'),
    provider: v.string(),
    lastSyncAt: v.number(),
    lastSyncStatus: v.union(
      v.literal('success'),
      v.literal('failed'),
      v.literal('in_progress')
    ),
    lastSyncDetails: v.optional(v.string()),
    eventsProcessed: v.optional(v.number()),
    eventsMatched: v.optional(v.number()),
    eventsUnmatched: v.optional(v.number()),
  })
    .index('by_client_id', ['clientId'])
    .index('by_client_and_provider', ['clientId', 'provider']),
});
