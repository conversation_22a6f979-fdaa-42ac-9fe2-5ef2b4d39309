'use node';

import { action } from '../_generated/server';
import { v } from 'convex/values';
import { encrypt as encryptText, decrypt as decryptText } from '../lib/encryption';

/**
 * Action to encrypt a plaintext string.
 * This runs in a Node.js environment and can use the 'crypto' module.
 */
export const encrypt = action({
  args: { text: v.string() },
  handler: async (_, { text }) => {
    return encryptText(text);
  },
});

/**
 * Action to decrypt a ciphertext string.
 * This runs in a Node.js environment and can use the 'crypto' module.
 */
export const decrypt = action({
  args: { text: v.string() },
  handler: async (_, { text }) => {
    return decryptText(text);
  },
}); 