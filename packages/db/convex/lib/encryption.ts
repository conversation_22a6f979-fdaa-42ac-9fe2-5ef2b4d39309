'use node';

import {
  createCipher<PERSON>,
  createDecipheriv,
  randomBytes,
  scryptSync,
} from 'node:crypto';

const ALGORITHM = 'aes-256-cbc';
const IV_LENGTH = 16;
const SALT = 'fitrewards-salt'; // A static salt is acceptable when using a strong, unique master key.

/**
 * Derives a consistent 32-byte encryption key from the environment variable.
 * @returns {Buffer} The derived 32-byte encryption key.
 * @throws {Error} If the ENCRYPTION_KEY is not set in the Convex environment.
 */
const getEncryptionKey = (): Buffer => {
  const masterKey = process.env.ENCRYPTION_KEY;
  if (!masterKey) {
    throw new Error(
      'ENCRYPTION_KEY environment variable not set in Convex dashboard. Please set it to a secure, 32-character random string.'
    );
  }
  return scryptSync(masterKey, SALT, 32);
};

/**
 * Encrypts a plaintext string using AES-256-CBC.
 * The IV is prepended to the ciphertext, separated by a colon.
 * @param {string} text - The plaintext to encrypt.
 * @returns {string} A string containing the IV and ciphertext, hex-encoded.
 */
export const encrypt = (text: string): string => {
  const iv = randomBytes(IV_LENGTH);
  const key = getEncryptionKey();
  const cipher = createCipheriv(ALGORITHM, key, iv);
  const encrypted = Buffer.concat([
    cipher.update(text, 'utf8'),
    cipher.final(),
  ]);
  return iv.toString('hex') + ':' + encrypted.toString('hex');
};

/**
 * Decrypts a string that was encrypted with the `encrypt` function.
 * @param {string} text - The IV and ciphertext string.
 * @returns {string} The decrypted plaintext.
 */
export const decrypt = (text: string): string => {
  const parts = text.split(':');
  if (parts.length !== 2) {
    throw new Error('Invalid encrypted text format.');
  }
  const iv = Buffer.from(parts[0], 'hex');
  const encryptedText = Buffer.from(parts[1], 'hex');
  const key = getEncryptionKey();
  const decipher = createDecipheriv(ALGORITHM, key, iv);
  const decrypted = Buffer.concat([
    decipher.update(encryptedText),
    decipher.final(),
  ]);
  return decrypted.toString('utf8');
};
