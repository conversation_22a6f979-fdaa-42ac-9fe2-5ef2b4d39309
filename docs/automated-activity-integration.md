# Automated Activity Integration Documentation

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Database Schema](#database-schema)
- [Mariana-Tek Integration](#mariana-tek-integration)
- [API Reference](#api-reference)
- [Admin Interface](#admin-interface)
- [Monitoring & Troubleshooting](#monitoring--troubleshooting)
- [Security Considerations](#security-considerations)
- [Testing & Validation](#testing--validation)
- [Future Integrations](#future-integrations)
- [Deployment Guide](#deployment-guide)

## Overview

The Automated Activity Integration system enables seamless connection between the FitRewards platform and third-party fitness management systems. This system automatically captures member check-ins from external platforms and logs them as activities in the rewards system, eliminating manual data entry and ensuring real-time point awards.

### Key Features

- **🔄 Automated Activity Logging**: Real-time capture of member check-ins
- **🎯 Intelligent User Matching**: Multi-stage user identification with fallback options
- **🔒 Secure Credential Management**: Encrypted storage of API keys and secrets
- **📊 Comprehensive Monitoring**: Real-time sync status and performance metrics
- **🛠️ Admin-Friendly Interface**: Easy setup and management through web UI
- **⚡ Duplicate Prevention**: Robust deduplication to prevent double-logging
- **🔍 Audit Trail**: Complete tracking of all automated activities

### Supported Integrations

- **Mariana-Tek** (Production Ready)
- **MindBody** (Planned)
- **ClassPass** (Future)
- **Glofox** (Future)

## Architecture

### System Overview

```mermaid
graph TB
    subgraph "External Systems"
        MT[Mariana-Tek API]
        MB[MindBody API]
        CP[ClassPass API]
    end

    subgraph "FitRewards Platform"
        CRON[Cron Jobs]
        SYNC[Sync Services]
        MATCH[User Matching]
        LOG[Activity Logging]
        ADMIN[Admin Interface]

        subgraph "Database"
            CREDS[API Credentials]
            USERS[Users]
            ACTIVITIES[Activities]
            UNMATCHED[Unmatched Events]
            SYNC_STATUS[Sync Status]
        end
    end

    MT -->|Poll every 5min| CRON
    CRON --> SYNC
    SYNC --> MATCH
    MATCH -->|Found| LOG
    MATCH -->|Not Found| UNMATCHED
    LOG --> ACTIVITIES
    ADMIN --> CREDS
    ADMIN --> UNMATCHED
```

### Data Flow Process

1. **Credential Setup**: Admin configures API credentials through web interface
2. **Automated Polling**: Cron job polls external APIs every 5 minutes
3. **Event Processing**: Check-in events are parsed and validated
4. **User Matching**: Multi-stage process to identify users in our system
5. **Activity Logging**: Successful matches trigger automatic activity logging
6. **Reward Processing**: Standard reward system processes points, milestones, and tiers
7. **Error Handling**: Unmatched events stored for manual admin review

### Integration Patterns

#### Polling-Based Integration (Mariana-Tek)

- **Frequency**: Every 5 minutes during business hours
- **Method**: REST API polling for recent check-ins
- **Advantages**: Simple implementation, works with any API
- **Disadvantages**: Slight delay (up to 5 minutes), higher API usage

#### Webhook-Based Integration (Future: MindBody)

- **Frequency**: Real-time as events occur
- **Method**: HTTP webhooks sent to our endpoint
- **Advantages**: Immediate processing, lower API usage
- **Disadvantages**: Requires webhook setup, more complex error handling

## Database Schema

### Enhanced Tables

#### Users Table Extensions

```sql
-- Added fields to existing users table
externalIds: Array<{
  provider: string,    -- "marianaTek", "mindBody", etc.
  userId: string       -- User ID in external system
}>
```

#### Activities Table Extensions

```sql
-- Added fields to existing activities table
source: "manual" | "webhook" | "api_poll"
externalReference: {
  provider: string,           -- External system name
  externalId: string,         -- External event/reservation ID
  externalTimestamp?: number  -- Original event timestamp
}
```

#### New Tables

##### clientApiCredentials

```sql
{
  _id: Id<"clientApiCredentials">,
  clientId: Id<"clients">,
  provider: string,              -- "marianaTek", "mindBody"
  encryptedApiKey: string,       -- Encrypted API key
  encryptedWebhookSecret?: string, -- Encrypted webhook secret
  isActive: boolean,
  lastTestAt?: number,
  lastTestStatus?: "success" | "failed",
  createdAt: number,
  updatedAt: number
}
```

##### unmatchedEvents

```sql
{
  _id: Id<"unmatchedEvents">,
  clientId: Id<"clients">,
  provider: string,
  externalUserId: string,
  externalUserEmail?: string,
  externalUserName?: string,
  eventType: string,             -- "check_in", "class_booking"
  eventData: object,             -- Raw event payload
  receivedAt: number,
  status: "pending" | "resolved" | "ignored",
  resolvedAt?: number,
  resolvedBy?: Id<"users">,
  notes?: string
}
```

##### integrationSyncStatus

```sql
{
  _id: Id<"integrationSyncStatus">,
  clientId: Id<"clients">,
  provider: string,
  lastSyncAt: number,
  lastSyncStatus: "success" | "failed" | "partial",
  lastSyncError?: string,
  eventsProcessed: number,
  eventsMatched: number,
  eventsUnmatched: number,
  nextSyncAt?: number
}
```

### Database Indexes

```typescript
// Optimized indexes for performance
users: {
  by_email: ['email'],
  by_external_id: ['externalIds.provider', 'externalIds.userId']
}

activities: {
  by_source: ['source'],
  by_external_reference: ['externalReference.provider', 'externalReference.externalId']
}

clientApiCredentials: {
  by_client_and_provider: ['clientId', 'provider']
}

unmatchedEvents: {
  by_client_and_status: ['clientId', 'status'],
  by_provider_and_external_user: ['provider', 'externalUserId']
}

integrationSyncStatus: {
  by_client_and_provider: ['clientId', 'provider']
}
```

## Mariana-Tek Integration

### API Overview

Mariana-Tek provides a REST API for accessing booking and user data. The integration uses polling-based synchronization due to limited webhook support.

#### Authentication

```typescript
// Bearer token authentication
headers: {
  'Authorization': `Bearer ${apiKey}`,
  'Content-Type': 'application/vnd.api+json'
}
```

#### Key Endpoints Used

##### Get Recent Check-ins

```http
GET /reservations/?checked_in_at__gte={timestamp}&status=checked_in
```

**Response Structure:**

```json
{
  "results": [
    {
      "id": "reservation_123",
      "user": {
        "id": "user_456",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe"
      },
      "class_session": {
        "id": "class_789",
        "name": "Morning Yoga",
        "class_session_type": {
          "name": "Yoga"
        }
      },
      "checked_in_at": "2024-01-15T10:30:00Z",
      "status": "checked_in"
    }
  ]
}
```

##### Test Connection

```http
GET /users/
```

### Integration Service

#### MarianaTekService Class

```typescript
export class MarianaTekService {
  constructor(apiKey: string);

  // Test API connectivity
  async testConnection(): Promise<{ success: boolean; error?: string }>;

  // Get recent check-ins since last sync
  async getRecentCheckIns(since: Date): Promise<CheckInEvent[]>;

  // Get user details for matching
  async getUserById(userId: string): Promise<MarianaTekUser | null>;

  // Search users by email
  async searchUsersByEmail(email: string): Promise<MarianaTekUser[]>;
}
```

#### Check-in Event Processing

```typescript
interface CheckInEvent {
  reservationId: string;
  userId: string;
  userEmail: string;
  userName?: string;
  classType: string;
  checkedInAt: Date;
  externalTimestamp: number;
}
```

### User Matching Algorithm

The system uses a three-stage approach to match external users:

#### Stage 1: External ID Lookup (Fastest)

```typescript
// Look for users with linked external IDs
const userWithExternalId = users.find((user) =>
  user.externalIds?.some(
    (ext) => ext.provider === 'marianaTek' && ext.userId === externalUserId
  )
);
```

#### Stage 2: Email Matching (Fallback)

```typescript
// Match by email address
const userByEmail = await ctx.db
  .query('users')
  .withIndex('by_email', (q) => q.eq('email', email))
  .filter((q) => q.eq(q.field('clientId'), clientId))
  .first();
```

#### Stage 3: Manual Review (Last Resort)

```typescript
// Store unmatched events for admin review
await storeUnmatchedEvent({
  clientId,
  provider: 'marianaTek',
  externalUserId,
  externalUserEmail,
  eventType: 'check_in',
  eventData: rawEvent,
});
```

### Sync Process

#### Cron Job Configuration

```typescript
// Runs every 5 minutes
crons.interval(
  'mariana-tek-sync',
  { minutes: 5 },
  internal.functions.marianaTekSync.syncMarianaTekCheckIns,
  {}
);
```

#### Sync Flow

1. **Get Active Clients**: Find all clients with active Mariana-Tek credentials
2. **Determine Sync Window**: Use last sync time or default to 1 hour ago
3. **Fetch Check-ins**: Poll Mariana-Tek API for recent check-ins
4. **Process Events**: Match users and log activities
5. **Update Status**: Record sync performance and errors
6. **Error Handling**: Continue processing even if individual events fail

### Error Handling

#### API Errors

```typescript
try {
  const response = await fetch(url, options);
  if (!response.ok) {
    throw new Error(`API request failed: ${response.status}`);
  }
} catch (error) {
  console.error('Mariana-Tek API error:', error);
  // Update sync status with error
  await updateSyncStatus({
    status: 'failed',
    error: error.message,
  });
}
```

#### Duplicate Prevention

```typescript
// Check for existing activity with same external reference
const existingActivity = await ctx.db
  .query('activities')
  .filter((q) =>
    q.and(
      q.eq(q.field('externalReference.provider'), 'marianaTek'),
      q.eq(q.field('externalReference.externalId'), reservationId)
    )
  )
  .first();

if (existingActivity) {
  return { success: true, duplicate: true };
}
```

## API Reference

### Core Functions

#### Integration Management

##### `configureIntegration`

```typescript
// Configure API credentials for a provider
await configureIntegration({
  provider: 'marianaTek',
  apiKey: 'your-api-key',
  webhookSecret?: 'optional-webhook-secret'
});
```

##### `testApiConnection`

```typescript
// Test API connectivity
const result = await testApiConnection({
  clientId: 'client_123',
  provider: 'marianaTek',
});
// Returns: { success: boolean, error?: string }
```

##### `getIntegrationStatus`

```typescript
// Get integration status for admin dashboard
const status = await getIntegrationStatus();
// Returns: { credentials: [...], syncStatuses: [...] }
```

#### Activity Logging

##### `internalLogActivity`

```typescript
// Log activity from automated systems
await internalLogActivity({
  userId: 'user_123',
  activityTypeKey: 'class_attendance',
  source: 'api_poll',
  externalReference: {
    provider: 'marianaTek',
    externalId: 'reservation_456',
    externalTimestamp: 1642248600000,
  },
});
```

#### User Matching

##### `findUserByExternalId`

```typescript
// Find user by external ID with email fallback
const match = await findUserByExternalId({
  provider: 'marianaTek',
  externalUserId: 'mt_user_123',
  email: '<EMAIL>',
  clientId: 'client_123',
});
// Returns: { user: User, matchType: 'external_id' | 'email' } | null
```

##### `linkExternalIdToUser`

```typescript
// Link external ID to existing user
await linkExternalIdToUser({
  userId: 'user_123',
  provider: 'marianaTek',
  externalUserId: 'mt_user_456',
});
```

#### Unmatched Events

##### `getUnmatchedEvents`

```typescript
// Get unmatched events for admin review
const events = await getUnmatchedEvents({
  status: 'pending',
  limit: 50,
});
```

##### `resolveUnmatchedEvent`

```typescript
// Resolve unmatched event by linking to user
await resolveUnmatchedEvent({
  eventId: 'event_123',
  userId: 'user_456',
  activityTypeKey: 'class_attendance',
  notes: 'Manually matched by admin',
});
```

### Sync Functions

##### `syncMarianaTekCheckIns`

```typescript
// Main sync function (called by cron)
const result = await syncMarianaTekCheckIns({
  clientId?: 'specific_client' // Optional, syncs all if omitted
});
// Returns: { clientsProcessed, totalEventsProcessed, ... }
```

##### `processCheckInEvent`

```typescript
// Process individual check-in event
await processCheckInEvent({
  clientId: 'client_123',
  event: {
    reservationId: 'res_123',
    userId: 'mt_user_456',
    userEmail: '<EMAIL>',
    classType: 'Yoga',
    checkedInAt: '2024-01-15T10:30:00Z',
    externalTimestamp: 1642248600000,
  },
});
```

## Admin Interface

### Integrations Tab

The admin interface provides a comprehensive dashboard for managing integrations:

#### Features

- **Integration Cards**: Visual status of each provider
- **Configuration Modals**: Step-by-step setup guides
- **Sync Status**: Real-time performance metrics
- **Unmatched Events**: Alerts for events requiring attention

#### Integration Status Indicators

- 🟢 **Connected**: API credentials valid, sync working
- 🟡 **Warning**: Credentials valid but sync issues
- 🔴 **Error**: API credentials invalid or connection failed
- ⚪ **Not Connected**: No credentials configured

#### Configuration Process

1. **Select Provider**: Choose from available integrations
2. **Enter Credentials**: API key and optional webhook secret
3. **Test Connection**: Automatic validation of credentials
4. **Save Configuration**: Encrypted storage of credentials
5. **Monitor Status**: Real-time sync monitoring

### Unmatched Events Management

#### Event Review Interface

- **Event List**: Paginated list of unmatched events
- **User Search**: Find users to link events to
- **Bulk Actions**: Process multiple events at once
- **Resolution Notes**: Track admin actions and decisions

#### Resolution Actions

- **Link to User**: Match event to existing user
- **Ignore Event**: Mark as not needing resolution
- **Bulk Ignore**: Process multiple similar events

## Monitoring & Troubleshooting

### Performance Metrics

#### Sync Status Tracking

```typescript
interface SyncStatus {
  provider: string;
  lastSyncAt: number;
  lastSyncStatus: 'success' | 'failed' | 'partial';
  eventsProcessed: number;
  eventsMatched: number;
  eventsUnmatched: number;
}
```

#### Key Performance Indicators

- **Sync Success Rate**: Percentage of successful sync operations
- **User Match Rate**: Percentage of events successfully matched to users
- **Processing Time**: Average time to process events
- **API Response Time**: External API performance
- **Error Rate**: Frequency of sync failures

### Common Issues & Solutions

#### High Unmatched Event Rate

**Symptoms**: Many events in unmatched queue
**Causes**:

- Users not properly linked to external system
- Email mismatches between systems
- New users not yet in FitRewards system

**Solutions**:

- Bulk link users by email matching
- Import user list from external system
- Improve onboarding process

#### Sync Failures

**Symptoms**: Failed sync status, no recent activities
**Causes**:

- Invalid API credentials
- External API downtime
- Rate limiting

**Solutions**:

- Test and refresh API credentials
- Check external system status
- Implement exponential backoff

#### Duplicate Activities

**Symptoms**: Users receiving double points
**Causes**:

- Duplicate detection failure
- Manual logging of automated events

**Solutions**:

- Review external reference matching
- Audit activity sources
- Implement stricter deduplication

### Logging & Debugging

#### Log Levels

```typescript
// Error: Critical failures requiring immediate attention
console.error('Mariana-Tek sync failed:', error);

// Warning: Issues that don't stop processing
console.warn('Failed to parse reservation:', reservation);

// Info: Normal operation status
console.log('Synced client ${clientId}: ${eventsProcessed} events');

// Debug: Detailed troubleshooting information
console.debug('Processing check-in event:', event);
```

#### Debug Tools

```typescript
// Test integration flow
await testIntegrationFlow({
  clientId: 'client_123',
  testUserEmail: '<EMAIL>',
});

// Simulate check-in events
await simulateMarianaTekCheckIn({
  clientId: 'client_123',
  userEmail: '<EMAIL>',
  externalUserId: 'mt_user_123',
});

// Clean up test data
await cleanupTestData({
  clientId: 'client_123',
});
```

## Security Considerations

### API Credential Protection

#### Encryption at Rest

```typescript
// Credentials are encrypted before storage
const encryptedApiKey = await encrypt(apiKey);
await saveApiCredentials({
  clientId,
  provider: 'marianaTek',
  encryptedApiKey,
  encryptedWebhookSecret,
});
```

#### Access Control

- **Admin Only**: Only admin users can configure integrations
- **Client Isolation**: Credentials scoped to specific clients
- **Audit Trail**: All configuration changes logged

#### Best Practices

- **Rotate Keys**: Regular API key rotation
- **Least Privilege**: Minimal required API permissions
- **Secure Transmission**: HTTPS for all API communications
- **Environment Separation**: Different keys for dev/staging/production

### Data Privacy

#### Personal Information Handling

- **Minimal Data**: Only collect necessary user information
- **Data Retention**: Automatic cleanup of old sync data
- **Anonymization**: Remove PII from logs and error messages

#### Compliance Considerations

- **GDPR**: Right to be forgotten, data portability
- **CCPA**: California privacy rights
- **HIPAA**: Healthcare information protection (if applicable)

### Webhook Security (Future)

#### Signature Verification

```typescript
// Verify webhook signatures
const signature = request.headers['x-webhook-signature'];
const payload = request.body;
const expectedSignature = generateSignature(payload, webhookSecret);

if (signature !== expectedSignature) {
  throw new Error('Invalid webhook signature');
}
```

#### Rate Limiting

- **Request Limits**: Prevent webhook flooding
- **IP Whitelisting**: Only accept from known sources
- **Timeout Protection**: Prevent long-running webhook handlers

## Testing & Validation

### Test Suite Structure

#### Unit Tests

```typescript
// Test individual functions
describe('MarianaTekService', () => {
  test('parseCheckInEvents handles valid data', () => {
    const mockResponse = { results: [mockReservation] };
    const events = service.parseCheckInEvents(mockResponse);
    expect(events).toHaveLength(1);
    expect(events[0].userEmail).toBe('<EMAIL>');
  });
});
```

#### Integration Tests

```typescript
// Test complete workflows
describe('Integration Flow', () => {
  test('processes check-in event end-to-end', async () => {
    const result = await testIntegrationFlow({
      clientId: 'test_client',
      testUserEmail: '<EMAIL>',
    });

    expect(result.steps.every((step) => step.success)).toBe(true);
  });
});
```

#### Load Tests

```typescript
// Test performance under load
describe('Performance', () => {
  test('handles 100 concurrent check-ins', async () => {
    const events = generateMockEvents(100);
    const startTime = Date.now();

    await Promise.all(events.map(processCheckInEvent));

    const duration = Date.now() - startTime;
    expect(duration).toBeLessThan(5000); // 5 second limit
  });
});
```

### Test Data Management

#### Mock Data Generation

```typescript
// Generate realistic test data
function generateMockCheckIn(overrides = {}) {
  return {
    reservationId: `test_res_${Date.now()}`,
    userId: `test_user_${Math.random()}`,
    userEmail: '<EMAIL>',
    classType: 'Test Class',
    checkedInAt: new Date().toISOString(),
    externalTimestamp: Date.now(),
    ...overrides,
  };
}
```

#### Test Environment Setup

```typescript
// Set up isolated test environment
beforeEach(async () => {
  await createTestClient();
  await createTestUsers();
  await createTestActivityTypes();
});

afterEach(async () => {
  await cleanupTestData();
});
```

### Validation Checklist

#### Pre-Deployment

- [ ] All unit tests passing
- [ ] Integration tests passing
- [ ] Load tests within acceptable limits
- [ ] Security scan completed
- [ ] API credentials tested
- [ ] Error handling validated
- [ ] Monitoring alerts configured

#### Post-Deployment

- [ ] Sync status monitoring active
- [ ] Error rates within normal range
- [ ] User match rates acceptable
- [ ] Performance metrics stable
- [ ] Admin interface functional
- [ ] Unmatched events processing correctly

## Future Integrations

### MindBody Integration (Next Priority)

MindBody is a leading fitness management platform with superior API capabilities compared to Mariana-Tek.

#### Advantages Over Mariana-Tek

- **Real-time Webhooks**: Immediate event processing instead of polling
- **Rich User Data**: Phone numbers, birth dates, membership details
- **Mature API**: Comprehensive documentation and SDKs
- **Self-service Access**: No contact required for API access
- **Better Rate Limits**: Higher throughput capabilities

#### Implementation Plan

##### Phase 1: API Service Development

```typescript
// packages/db/convex/lib/MindBodyService.ts
export class MindBodyService {
  constructor(apiKey: string, siteId: string);

  // Real-time webhook setup
  async setupWebhook(callbackUrl: string): Promise<void>;

  // Enhanced user data
  async getUserById(userId: string): Promise<MindBodyUser>;

  // Class check-in events
  async getRecentCheckIns(since: Date): Promise<CheckInEvent[]>;

  // Membership and package data
  async getUserMembership(userId: string): Promise<MembershipInfo>;
}
```

##### Phase 2: Webhook Implementation

```typescript
// packages/db/convex/functions/mindBodyWebhook.ts
export const handleMindBodyWebhook = httpAction({
  args: {
    /* webhook payload */
  },
  async handler(ctx, request) {
    // Verify webhook signature
    await verifyWebhookSignature(request);

    // Process event immediately
    await processWebhookEvent(request.body);

    return new Response('OK', { status: 200 });
  },
});
```

##### Phase 3: Enhanced User Matching

```typescript
// Additional matching fields for MindBody
interface MindBodyUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  mobilePhone?: string; // Additional matching field
  birthDate?: string; // Additional matching field
  membershipId?: string; // Unique membership identifier
}

// Enhanced matching algorithm
async function findMindBodyUser(userData: MindBodyUser) {
  // 1. External ID lookup
  // 2. Email matching
  // 3. Phone number matching (new)
  // 4. Name + birth date matching (new)
  // 5. Manual review
}
```

#### Integration Comparison

| Feature                | Mariana-Tek | MindBody    | ClassPass   | Glofox      |
| ---------------------- | ----------- | ----------- | ----------- | ----------- |
| **Real-time Events**   | ❌ Polling  | ✅ Webhooks | ✅ Webhooks | ✅ Webhooks |
| **User Data Richness** | Basic       | Rich        | Limited     | Moderate    |
| **API Documentation**  | Limited     | Excellent   | Good        | Good        |
| **Setup Complexity**   | High        | Low         | Moderate    | Moderate    |
| **Rate Limits**        | Unknown     | High        | Moderate    | Moderate    |
| **Market Share**       | Small       | Large       | Large       | Growing     |

### ClassPass Integration (Future)

ClassPass provides access to multiple fitness studios through a single platform.

#### Unique Considerations

- **Multi-studio Events**: Users can check into different studios
- **Credit System**: Different classes cost different credits
- **Partner Studios**: Need to map ClassPass studios to client locations

#### Implementation Approach

```typescript
// Enhanced event data for ClassPass
interface ClassPassEvent {
  reservationId: string;
  userId: string;
  studioId: string; // Map to client location
  classId: string;
  creditsUsed: number; // Could influence points awarded
  partnerStudio: boolean; // Different handling for partner vs owned
}
```

### Glofox Integration (Future)

Glofox is popular with boutique fitness studios and offers comprehensive APIs.

#### Key Features

- **Membership Management**: Detailed membership and package tracking
- **Payment Integration**: Revenue tracking capabilities
- **Custom Fields**: Studio-specific data collection
- **Multi-location Support**: Chain management features

### Universal Integration Framework

#### Standardized Interface

```typescript
// Abstract base class for all integrations
abstract class FitnessIntegrationService {
  abstract testConnection(): Promise<ConnectionResult>;
  abstract getRecentCheckIns(since: Date): Promise<CheckInEvent[]>;
  abstract getUserById(id: string): Promise<ExternalUser>;
  abstract setupWebhook?(url: string): Promise<void>;
}

// Provider-specific implementations
class MarianaTekService extends FitnessIntegrationService {}
class MindBodyService extends FitnessIntegrationService {}
class ClassPassService extends FitnessIntegrationService {}
```

#### Configuration Schema

```typescript
interface IntegrationConfig {
  provider: 'marianaTek' | 'mindBody' | 'classPass' | 'glofox';
  credentials: {
    apiKey: string;
    siteId?: string; // MindBody, Glofox
    webhookSecret?: string; // All except Mariana-Tek
    environment?: 'sandbox' | 'production';
  };
  settings: {
    syncFrequency: number; // Minutes between polls
    enableWebhooks: boolean;
    userMatchingStrict: boolean;
    autoLinkByEmail: boolean;
  };
}
```

## Advanced Features (Future)

### Intelligent User Matching

#### Machine Learning Enhancement

```typescript
// AI-powered user matching
interface UserMatchingML {
  // Fuzzy name matching
  matchByName(externalName: string, candidates: User[]): MatchResult[];

  // Behavioral pattern matching
  matchByActivity(
    externalHistory: Activity[],
    candidates: User[]
  ): MatchResult[];

  // Confidence scoring
  calculateMatchConfidence(match: UserMatch): number;
}
```

#### Progressive Matching

- **Initial**: Exact email/ID matching
- **Enhanced**: Fuzzy name and phone matching
- **Advanced**: Behavioral pattern analysis
- **ML-Powered**: Predictive matching based on activity patterns

### Multi-Provider Sync

#### Unified Dashboard

```typescript
// Aggregate data from multiple providers
interface UnifiedSyncStatus {
  totalProviders: number;
  activeProviders: number;
  totalEventsToday: number;
  matchRateAverage: number;
  providerBreakdown: ProviderStatus[];
}
```

#### Cross-Provider Deduplication

```typescript
// Prevent duplicate activities from multiple sources
interface ActivityDeduplication {
  // Time-based deduplication
  checkTimeWindow(activity: Activity, windowMinutes: number): boolean;

  // Location-based deduplication
  checkLocationMatch(activity1: Activity, activity2: Activity): boolean;

  // User confirmation for ambiguous cases
  requireUserConfirmation(activities: Activity[]): boolean;
}
```

### Analytics & Insights

#### Integration Performance Analytics

```typescript
interface IntegrationAnalytics {
  // Provider performance comparison
  providerEfficiency: {
    provider: string;
    avgProcessingTime: number;
    errorRate: number;
    userSatisfaction: number;
  }[];

  // User engagement impact
  automationImpact: {
    manualVsAutomated: {
      manual: { activities: number; engagement: number };
      automated: { activities: number; engagement: number };
    };
    retentionImprovement: number;
    pointsAccuracy: number;
  };
}
```

#### Predictive Maintenance

```typescript
// Predict and prevent integration issues
interface PredictiveMaintenance {
  // API health monitoring
  predictApiDowntime(provider: string): DowntimePrediction;

  // User matching degradation detection
  detectMatchingIssues(): MatchingHealthReport;

  // Capacity planning
  predictScalingNeeds(growthRate: number): ScalingRecommendations;
}
```

### Enterprise Features

#### Multi-Tenant Isolation

```typescript
// Enhanced security for enterprise clients
interface EnterpriseSecurity {
  // Tenant-specific encryption keys
  tenantEncryption: Map<string, EncryptionKey>;

  // Audit logging
  auditTrail: AuditEvent[];

  // Compliance reporting
  generateComplianceReport(standard: 'SOC2' | 'HIPAA' | 'GDPR'): Report;
}
```

#### Advanced Monitoring

```typescript
// Enterprise-grade monitoring
interface EnterpriseMonitoring {
  // Real-time alerting
  alerting: {
    syncFailures: AlertConfig;
    highUnmatchedRate: AlertConfig;
    apiLatency: AlertConfig;
    securityEvents: AlertConfig;
  };

  // Custom dashboards
  dashboards: CustomDashboard[];

  // SLA monitoring
  slaTracking: SLAMetrics;
}
```

## Deployment Guide

### Prerequisites

#### System Requirements

- **Convex Database**: Latest version with cron job support
- **Node.js**: Version 18+ for optimal performance
- **Environment Variables**: Secure credential storage
- **HTTPS**: Required for webhook endpoints

#### Dependencies

```json
{
  "dependencies": {
    "convex": "^1.0.0",
    "@convex-dev/auth": "^0.0.1",
    "crypto": "built-in"
  }
}
```

### Environment Configuration

#### Development Environment

```bash
# .env.local
CONVEX_DEPLOYMENT=dev:your-deployment
NEXT_PUBLIC_CONVEX_URL=https://your-deployment.convex.cloud

# Integration testing
MARIANA_TEK_TEST_API_KEY=test_key_here
MARIANA_TEK_SANDBOX_URL=https://sandbox.marianatek.com
```

#### Production Environment

```bash
# .env.production
CONVEX_DEPLOYMENT=prod:your-deployment
NEXT_PUBLIC_CONVEX_URL=https://your-deployment.convex.cloud

# Encryption keys (rotate regularly)
INTEGRATION_ENCRYPTION_KEY=your-encryption-key
WEBHOOK_SIGNING_SECRET=your-webhook-secret
```

### Database Migration

#### Schema Deployment

```bash
# Deploy schema changes
npx convex deploy --cmd="schema"

# Verify schema deployment
npx convex dashboard
```

#### Data Migration Script

```typescript
// Migration script for existing installations
export const migrateToIntegrations = internalMutation({
  async handler(ctx) {
    // Add externalIds field to existing users
    const users = await ctx.db.query('users').collect();
    for (const user of users) {
      if (!user.externalIds) {
        await ctx.db.patch(user._id, { externalIds: [] });
      }
    }

    // Add source field to existing activities
    const activities = await ctx.db.query('activities').collect();
    for (const activity of activities) {
      if (!activity.source) {
        await ctx.db.patch(activity._id, { source: 'manual' });
      }
    }
  },
});
```

### Cron Job Setup

#### Production Scheduling

```typescript
// Optimized cron schedule for production
const crons = cronJobs();

// Business hours sync (more frequent)
crons.interval(
  'mariana-tek-sync-business',
  { minutes: 5 },
  internal.functions.marianaTekSync.syncMarianaTekCheckIns,
  {},
  {
    schedule: 'Mon-Fri 6:00-22:00 PST', // Business hours only
  }
);

// Off-hours sync (less frequent)
crons.interval(
  'mariana-tek-sync-offhours',
  { minutes: 30 },
  internal.functions.marianaTekSync.syncMarianaTekCheckIns,
  {},
  {
    schedule: 'Mon-Fri 22:00-6:00 PST, Sat-Sun 0:00-23:59 PST',
  }
);
```

### Monitoring Setup

#### Health Checks

```typescript
// Health check endpoint
export const healthCheck = httpAction({
  async handler(ctx) {
    const checks = {
      database: await testDatabaseConnection(ctx),
      integrations: await testIntegrationConnections(ctx),
      cronJobs: await testCronJobStatus(ctx),
    };

    const allHealthy = Object.values(checks).every((check) => check.healthy);

    return new Response(JSON.stringify(checks), {
      status: allHealthy ? 200 : 503,
      headers: { 'Content-Type': 'application/json' },
    });
  },
});
```

#### Alerting Configuration

```typescript
// Alert thresholds
const ALERT_THRESHOLDS = {
  syncFailureRate: 0.1, // 10% failure rate
  unmatchedEventRate: 0.3, // 30% unmatched rate
  apiLatency: 5000, // 5 second response time
  processingDelay: 300000, // 5 minute processing delay
};
```

### Security Hardening

#### API Key Rotation

```typescript
// Automated key rotation
export const rotateApiKeys = internalAction({
  async handler(ctx) {
    const credentials = await ctx.runQuery(
      internal.functions.integrations.getAllActiveCredentials
    );

    for (const cred of credentials) {
      if (shouldRotateKey(cred)) {
        await rotateCredential(cred);
        await notifyAdmins(cred.clientId, 'API key rotated');
      }
    }
  },
});
```

#### Access Control

```typescript
// Role-based access control
const INTEGRATION_PERMISSIONS = {
  admin: ['configure', 'test', 'monitor', 'resolve'],
  staff: ['monitor', 'resolve'],
  user: [],
};
```

### Performance Optimization

#### Database Optimization

```sql
-- Optimized indexes for integration queries
CREATE INDEX idx_activities_external_ref
ON activities (externalReference.provider, externalReference.externalId);

CREATE INDEX idx_users_external_ids
ON users USING GIN (externalIds);

CREATE INDEX idx_unmatched_events_status_time
ON unmatchedEvents (status, receivedAt DESC);
```

#### Caching Strategy

```typescript
// Cache frequently accessed data
const CACHE_CONFIG = {
  userMatches: { ttl: 300 }, // 5 minutes
  apiCredentials: { ttl: 3600 }, // 1 hour
  syncStatus: { ttl: 60 }, // 1 minute
};
```

### Rollback Procedures

#### Emergency Rollback

```bash
# Disable integrations immediately
npx convex run internal.functions.integrations.disableAllIntegrations

# Rollback database schema
npx convex deploy --cmd="schema" --version="previous"

# Restore from backup if needed
npx convex import backup-timestamp.jsonl
```

#### Gradual Rollback

```typescript
// Feature flag for gradual rollback
export const INTEGRATION_FEATURE_FLAGS = {
  enableMarianaTekSync: true,
  enableWebhooks: false,
  enableAutoMatching: true,
  enableBulkProcessing: false,
};
```

This comprehensive documentation provides everything needed to understand, implement, maintain, and extend the automated activity integration system. The system is designed to be robust, scalable, and ready for production deployment while providing a solid foundation for future integrations.
