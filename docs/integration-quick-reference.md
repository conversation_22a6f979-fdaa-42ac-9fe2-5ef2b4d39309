# Automated Activity Integration - Quick Reference

## Overview
This document provides a quick reference for developers working with the automated activity integration system.

## Key Files & Functions

### Database Schema
- **Location**: `packages/db/convex/schema.ts`
- **New Tables**: `clientApiCredentials`, `unmatchedEvents`, `integrationSyncStatus`
- **Enhanced Tables**: `users` (externalIds), `activities` (source, externalReference)

### Core Functions

#### Activity Logging
```typescript
// Enhanced activity logging with automation support
import { api } from '@fitness-rewards/db';

// Manual activity logging (existing)
await api.functions.activities.logActivity({
  activityTypeKey: 'class_attendance'
});

// Automated activity logging (new)
await api.functions.activities.internalLogActivity({
  userId: 'user_123',
  activityTypeKey: 'class_attendance',
  source: 'api_poll',
  externalReference: {
    provider: 'marianaTek',
    externalId: 'reservation_456',
    externalTimestamp: 1642248600000
  }
});
```

#### Integration Management
```typescript
// Configure integration
await api.functions.integrations.configureIntegration({
  provider: 'marianaTek',
  apiKey: 'your-api-key',
  webhookSecret: 'optional-secret'
});

// Test connection
const result = await api.functions.integrations.testApiConnection({
  clientId: 'client_123',
  provider: 'marianaTek'
});

// Get integration status
const status = await api.functions.integrations.getIntegrationStatus();
```

#### User Matching
```typescript
// Find user by external ID
const match = await api.functions.integrations.findUserByExternalId({
  provider: 'marianaTek',
  externalUserId: 'mt_user_123',
  email: '<EMAIL>',
  clientId: 'client_123'
});

// Link external ID to user
await api.functions.integrations.linkExternalIdToUser({
  userId: 'user_123',
  provider: 'marianaTek',
  externalUserId: 'mt_user_456'
});
```

#### Sync Operations
```typescript
// Manual sync trigger
await api.functions.marianaTekSync.syncMarianaTekCheckIns({
  clientId: 'client_123' // Optional
});

// Process individual event
await api.functions.marianaTekSync.processCheckInEvent({
  clientId: 'client_123',
  event: checkInEventData
});
```

### Mariana-Tek Service
```typescript
// Location: packages/db/convex/lib/MarianaTekService.ts
import { MarianaTekService } from '../lib/MarianaTekService';

const service = new MarianaTekService(apiKey);

// Test connection
const testResult = await service.testConnection();

// Get recent check-ins
const events = await service.getRecentCheckIns(new Date('2024-01-01'));

// Get user details
const user = await service.getUserById('mt_user_123');
```

## Data Structures

### Check-in Event
```typescript
interface CheckInEvent {
  reservationId: string;
  userId: string;
  userEmail: string;
  userName?: string;
  classType: string;
  checkedInAt: Date;
  externalTimestamp: number;
}
```

### External Reference
```typescript
interface ExternalReference {
  provider: string;           // 'marianaTek', 'mindBody', etc.
  externalId: string;         // External system's event/reservation ID
  externalTimestamp?: number; // Original timestamp from external system
}
```

### User External ID
```typescript
interface ExternalId {
  provider: string;  // 'marianaTek', 'mindBody', etc.
  userId: string;    // User ID in external system
}
```

## Admin UI Components

### Integration Tab
- **Location**: `apps/web/src/components/admin/IntegrationsTab.tsx`
- **Features**: Configuration, status monitoring, unmatched events

### Key UI States
```typescript
// Integration status indicators
type IntegrationStatus = 
  | 'connected'     // Green - Working properly
  | 'warning'       // Yellow - Issues but functional
  | 'error'         // Red - Connection failed
  | 'not_connected' // Gray - Not configured
```

## Cron Jobs

### Configuration
```typescript
// Location: packages/db/convex/crons.ts
crons.interval(
  'mariana-tek-sync',
  { minutes: 5 },
  internal.functions.marianaTekSync.syncMarianaTekCheckIns,
  {}
);
```

### Manual Triggers
```bash
# Trigger sync manually
npx convex run internal.functions.marianaTekSync.syncMarianaTekCheckIns

# Test integration flow
npx convex run internal.functions.testIntegration.testIntegrationFlow \
  --clientId "client_123" \
  --testUserEmail "<EMAIL>"
```

## Common Patterns

### Error Handling
```typescript
try {
  const result = await processCheckInEvent(event);
  if (result.matched) {
    console.log(`Activity logged: ${result.pointsAwarded} points`);
  } else {
    console.log('Event stored for manual review');
  }
} catch (error) {
  console.error('Processing failed:', error);
  // Continue with next event
}
```

### Duplicate Prevention
```typescript
// Check for existing activity
const existing = await ctx.db
  .query('activities')
  .filter(q => 
    q.and(
      q.eq(q.field('externalReference.provider'), provider),
      q.eq(q.field('externalReference.externalId'), externalId)
    )
  )
  .first();

if (existing) {
  return { success: true, duplicate: true };
}
```

### User Matching Flow
```typescript
// 1. Try external ID lookup
let user = await findByExternalId(provider, externalUserId);

if (!user && email) {
  // 2. Try email matching
  user = await findByEmail(email);
  
  if (user) {
    // Link external ID for future lookups
    await linkExternalIdToUser(user._id, provider, externalUserId);
  }
}

if (!user) {
  // 3. Store for manual review
  await storeUnmatchedEvent(eventData);
}
```

## Testing

### Test Functions
```typescript
// Simulate check-in event
await api.functions.testIntegration.simulateMarianaTekCheckIn({
  clientId: 'client_123',
  userEmail: '<EMAIL>',
  externalUserId: 'mt_test_user'
});

// Create sample unmatched events
await api.functions.testIntegration.createSampleUnmatchedEvents({
  clientId: 'client_123',
  count: 5
});

// Clean up test data
await api.functions.testIntegration.cleanupTestData({
  clientId: 'client_123'
});
```

### Debug Queries
```typescript
// Check sync status
const syncStatus = await ctx.db
  .query('integrationSyncStatus')
  .withIndex('by_client_and_provider', q => 
    q.eq('clientId', clientId).eq('provider', 'marianaTek')
  )
  .first();

// Find unmatched events
const unmatched = await ctx.db
  .query('unmatchedEvents')
  .withIndex('by_client_and_status', q => 
    q.eq('clientId', clientId).eq('status', 'pending')
  )
  .collect();

// Check recent activities
const recentActivities = await ctx.db
  .query('activities')
  .withIndex('by_source', q => q.eq('source', 'api_poll'))
  .order('desc')
  .take(10);
```

## Monitoring

### Key Metrics
- **Sync Success Rate**: `lastSyncStatus === 'success'`
- **User Match Rate**: `eventsMatched / eventsProcessed`
- **Processing Latency**: Time between external event and activity logging
- **Unmatched Event Queue**: Count of pending unmatched events

### Health Checks
```typescript
// Check integration health
const health = {
  apiConnection: await testApiConnection(),
  recentSync: syncStatus?.lastSyncAt > Date.now() - 600000, // 10 min
  unmatchedCount: await getUnmatchedEventsCount(),
  errorRate: calculateErrorRate(syncHistory)
};
```

## Troubleshooting

### Common Issues

#### High Unmatched Rate
```typescript
// Check user email consistency
const users = await ctx.db.query('users').collect();
const emailDomains = users.map(u => u.email.split('@')[1]);
console.log('Email domains:', [...new Set(emailDomains)]);
```

#### Sync Failures
```typescript
// Check API credentials
const creds = await getApiCredentials(clientId, 'marianaTek');
if (creds?.lastTestStatus === 'failed') {
  console.log('API credentials may be invalid');
}
```

#### Duplicate Activities
```typescript
// Find potential duplicates
const duplicates = await ctx.db
  .query('activities')
  .filter(q => q.eq(q.field('source'), 'api_poll'))
  .collect()
  .then(activities => 
    activities.filter((a, i, arr) => 
      arr.findIndex(b => 
        b.userId === a.userId && 
        Math.abs(b.timestamp - a.timestamp) < 60000 // 1 minute
      ) !== i
    )
  );
```

## Environment Variables

### Development
```bash
CONVEX_DEPLOYMENT=dev:your-deployment
MARIANA_TEK_TEST_API_KEY=test_key_here
```

### Production
```bash
CONVEX_DEPLOYMENT=prod:your-deployment
INTEGRATION_ENCRYPTION_KEY=your-encryption-key
```

## Quick Commands

```bash
# Deploy schema changes
npx convex deploy

# View logs
npx convex logs

# Run function manually
npx convex run functions.marianaTekSync.syncMarianaTekCheckIns

# Open dashboard
npx convex dashboard
```

This quick reference provides the essential information developers need to work with the automated activity integration system efficiently.
